import { serve } from "https://deno.land/std@0.168.0/http/server.ts";
import { EmailService } from "../_shared/email-service.ts";

// CORS headers to allow cross-origin requests
const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
  'Access-Control-Allow-Methods': 'POST, OPTIONS'
};

serve(async (req) => {
  // Handle CORS preflight requests
  if (req.method === 'OPTIONS') {
    return new Response(null, {
      status: 204,
      headers: corsHeaders
    });
  }

  if (req.method !== 'POST') {
    return new Response(
      JSON.stringify({ error: "Method not allowed" }),
      { status: 405, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
    );
  }

  try {
    // Parse request body
    const { certificate_id, email_type } = await req.json();

    if (!certificate_id) {
      return new Response(
        JSON.stringify({ error: "certificate_id is required" }),
        { status: 400, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
      );
    }

    if (!email_type || !['customer_success', 'customer_failure', 'admin_success', 'admin_failure'].includes(email_type)) {
      return new Response(
        JSON.stringify({ error: "Valid email_type is required (customer_success, customer_failure, admin_success, admin_failure)" }),
        { status: 400, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
      );
    }

    console.log(`Testing email service for certificate ${certificate_id} with type ${email_type}`);

    // Initialize email service
    const emailService = new EmailService();

    let result: boolean = false;
    let message: string = '';

    // Send the appropriate email based on type
    switch (email_type) {
      case 'customer_success':
        result = await emailService.sendCustomerSuccessNotification(certificate_id);
        message = result ? 'Customer success email sent successfully' : 'Failed to send customer success email';
        break;
      case 'customer_failure':
        result = await emailService.sendCustomerFailureNotification(certificate_id);
        message = result ? 'Customer failure email sent successfully' : 'Failed to send customer failure email';
        break;
      case 'admin_success':
        result = await emailService.sendAdminSuccessNotification(certificate_id);
        message = result ? 'Admin success email sent successfully' : 'Failed to send admin success email';
        break;
      case 'admin_failure':
        result = await emailService.sendAdminFailureNotification(certificate_id);
        message = result ? 'Admin failure email sent successfully' : 'Failed to send admin failure email';
        break;
    }

    console.log(`Email test result: ${message}`);

    return new Response(
      JSON.stringify({ 
        success: result,
        message,
        certificate_id,
        email_type
      }),
      { 
        status: result ? 200 : 500, 
        headers: { ...corsHeaders, 'Content-Type': 'application/json' } 
      }
    );

  } catch (error) {
    console.error(`Error in test-email function: ${error.message}`);
    
    return new Response(
      JSON.stringify({ 
        error: error.message,
        success: false
      }),
      { 
        status: 500, 
        headers: { ...corsHeaders, 'Content-Type': 'application/json' } 
      }
    );
  }
});

/* 
Usage examples:

Test customer success email:
POST /functions/v1/test-email
{
  "certificate_id": "your-certificate-id",
  "email_type": "customer_success"
}

Test customer failure email:
POST /functions/v1/test-email
{
  "certificate_id": "your-certificate-id",
  "email_type": "customer_failure"
}

Test admin success email:
POST /functions/v1/test-email
{
  "certificate_id": "your-certificate-id",
  "email_type": "admin_success"
}

Test admin failure email:
POST /functions/v1/test-email
{
  "certificate_id": "your-certificate-id",
  "email_type": "admin_failure"
}
*/
