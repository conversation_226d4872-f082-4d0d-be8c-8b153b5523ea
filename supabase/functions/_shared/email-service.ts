import { Resend } from "https://esm.sh/resend@2.1.0";
import { createClient } from "https://esm.sh/@supabase/supabase-js@2.49.1";

// Email types
export type EmailType = 'customer_success' | 'customer_failure' | 'admin_success' | 'admin_failure';

// Email status
export type EmailStatus = 'sent' | 'failed' | 'pending';

// Email log interface
export interface EmailLog {
  id?: string;
  certificate_id: string;
  recipient_email: string;
  email_type: EmailType;
  sent_at?: string;
  status: EmailStatus;
  error_message?: string;
  resend_message_id?: string;
}

// Certificate data interface
export interface CertificateData {
  id: string;
  order_number: string;
  certificate_type: string;
  payment_status: string;
  user_id: string;
  objektdaten?: any;
  gebaeudedetails1?: any;
  created_at: string;
}

// User data interface
export interface UserData {
  id: string;
  email: string;
  full_name?: string;
}

// Email service class
export class EmailService {
  private resend: Resend;
  private supabase: any;
  private fromEmail: string;
  private adminEmail: string;

  constructor() {
    const resendApiKey = Deno.env.get("RESEND_API_KEY");
    const supabaseUrl = Deno.env.get("SUPABASE_URL");
    const supabaseServiceKey = Deno.env.get("SUPABASE_SERVICE_ROLE_KEY");

    this.fromEmail = Deno.env.get("FROM_EMAIL") || "<EMAIL>";
    this.adminEmail = Deno.env.get("ADMIN_EMAIL") || "<EMAIL>";

    if (!resendApiKey) {
      throw new Error("RESEND_API_KEY environment variable is required");
    }
    if (!supabaseUrl || !supabaseServiceKey) {
      throw new Error("Supabase environment variables are required");
    }

    this.resend = new Resend(resendApiKey);
    this.supabase = createClient(supabaseUrl, supabaseServiceKey);
  }

  // Log email attempt to database
  private async logEmail(emailLog: EmailLog): Promise<string | null> {
    try {
      const { data, error } = await this.supabase
        .from('email_logs')
        .insert(emailLog)
        .select('id')
        .single();

      if (error) {
        console.error('Error logging email:', error);
        return null;
      }

      return data?.id || null;
    } catch (error) {
      console.error('Error logging email:', error);
      return null;
    }
  }

  // Update email log status
  private async updateEmailLog(logId: string, updates: Partial<EmailLog>): Promise<void> {
    try {
      const { error } = await this.supabase
        .from('email_logs')
        .update(updates)
        .eq('id', logId);

      if (error) {
        console.error('Error updating email log:', error);
      }
    } catch (error) {
      console.error('Error updating email log:', error);
    }
  }

  // Get user email from auth.users table
  private async getUserEmail(userId: string): Promise<string | null> {
    try {
      const { data, error } = await this.supabase.auth.admin.getUserById(userId);

      if (error || !data?.user?.email) {
        console.error('Error fetching user email:', error);
        return null;
      }

      return data.user.email;
    } catch (error) {
      console.error('Error fetching user email:', error);
      return null;
    }
  }

  // Get certificate and user data
  private async getCertificateData(certificateId: string): Promise<{ certificate: CertificateData; userEmail: string } | null> {
    try {
      const { data: certificate, error: certError } = await this.supabase
        .from('energieausweise')
        .select('*')
        .eq('id', certificateId)
        .single();

      if (certError || !certificate) {
        console.error('Error fetching certificate:', certError);
        return null;
      }

      const userEmail = await this.getUserEmail(certificate.user_id);
      if (!userEmail) {
        console.error('Could not fetch user email for certificate:', certificateId);
        return null;
      }

      return { certificate, userEmail };
    } catch (error) {
      console.error('Error fetching certificate data:', error);
      return null;
    }
  }

  // Generate customer success email content
  private generateCustomerSuccessEmail(certificate: CertificateData, userEmail: string): { subject: string; html: string } {
    const certificateTypeMap: Record<string, string> = {
      'WG/V': 'Wohngebäude Verbrauchsausweis',
      'WG/B': 'Wohngebäude Bedarfsausweis',
      'NWG/V': 'Nichtwohngebäude Verbrauchsausweis'
    };

    const certificateTypeName = certificateTypeMap[certificate.certificate_type] || certificate.certificate_type;
    const buildingAddress = certificate.objektdaten?.strasse && certificate.objektdaten?.hausnummer
      ? `${certificate.objektdaten.strasse} ${certificate.objektdaten.hausnummer}, ${certificate.objektdaten.plz} ${certificate.objektdaten.ort}`
      : 'Ihre Immobilie';

    const subject = `Zahlungsbestätigung - Energieausweis ${certificate.order_number}`;

    const html = `
      <!DOCTYPE html>
      <html>
      <head>
        <meta charset="utf-8">
        <title>Zahlungsbestätigung</title>
        <style>
          body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
          .container { max-width: 600px; margin: 0 auto; padding: 20px; }
          .header { background-color: #16a34a; color: white; padding: 20px; text-align: center; }
          .content { padding: 20px; background-color: #f9f9f9; }
          .details { background-color: white; padding: 15px; margin: 15px 0; border-radius: 5px; }
          .footer { text-align: center; padding: 20px; font-size: 12px; color: #666; }
        </style>
      </head>
      <body>
        <div class="container">
          <div class="header">
            <h1>Zahlungsbestätigung</h1>
          </div>
          <div class="content">
            <p>Vielen Dank für Ihre Bestellung! Ihre Zahlung wurde erfolgreich verarbeitet.</p>

            <div class="details">
              <h3>Bestelldetails:</h3>
              <p><strong>Bestellnummer:</strong> ${certificate.order_number}</p>
              <p><strong>Energieausweis-Typ:</strong> ${certificateTypeName}</p>
              <p><strong>Immobilie:</strong> ${buildingAddress}</p>
              <p><strong>Bestelldatum:</strong> ${new Date(certificate.created_at).toLocaleDateString('de-DE')}</p>
            </div>

            <p>Ihr Energieausweis wird nun erstellt und Sie erhalten ihn in Kürze per E-Mail.</p>

            <p>Bei Fragen stehen wir Ihnen gerne zur Verfügung.</p>

            <p>Mit freundlichen Grüßen<br>
            Ihr Energieausweis-Team</p>
          </div>
          <div class="footer">
            <p>Diese E-Mail wurde automatisch generiert. Bitte antworten Sie nicht auf diese E-Mail.</p>
          </div>
        </div>
      </body>
      </html>
    `;

    return { subject, html };
  }

  // Generate customer failure email content
  private generateCustomerFailureEmail(certificate: CertificateData, userEmail: string): { subject: string; html: string } {
    const subject = `Zahlungsproblem - Energieausweis ${certificate.order_number}`;

    const html = `
      <!DOCTYPE html>
      <html>
      <head>
        <meta charset="utf-8">
        <title>Zahlungsproblem</title>
        <style>
          body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
          .container { max-width: 600px; margin: 0 auto; padding: 20px; }
          .header { background-color: #dc2626; color: white; padding: 20px; text-align: center; }
          .content { padding: 20px; background-color: #f9f9f9; }
          .details { background-color: white; padding: 15px; margin: 15px 0; border-radius: 5px; }
          .footer { text-align: center; padding: 20px; font-size: 12px; color: #666; }
          .retry-button { background-color: #16a34a; color: white; padding: 12px 24px; text-decoration: none; border-radius: 5px; display: inline-block; margin: 15px 0; }
        </style>
      </head>
      <body>
        <div class="container">
          <div class="header">
            <h1>Zahlungsproblem</h1>
          </div>
          <div class="content">
            <p>Leider konnte Ihre Zahlung für den Energieausweis nicht verarbeitet werden.</p>

            <div class="details">
              <h3>Bestelldetails:</h3>
              <p><strong>Bestellnummer:</strong> ${certificate.order_number}</p>
              <p><strong>Bestelldatum:</strong> ${new Date(certificate.created_at).toLocaleDateString('de-DE')}</p>
            </div>

            <p>Mögliche Gründe für das Zahlungsproblem:</p>
            <ul>
              <li>Unzureichende Deckung auf dem Konto</li>
              <li>Falsche Kartendaten</li>
              <li>Technisches Problem bei der Zahlungsabwicklung</li>
            </ul>

            <p>Sie können die Zahlung jederzeit erneut versuchen:</p>
            <a href="${Deno.env.get('SITE_URL') || 'https://energieausweis.com'}/meine-zertifikate" class="retry-button">Zahlung erneut versuchen</a>

            <p>Bei anhaltenden Problemen kontaktieren Sie uns bitte.</p>

            <p>Mit freundlichen Grüßen<br>
            Ihr Energieausweis-Team</p>
          </div>
          <div class="footer">
            <p>Diese E-Mail wurde automatisch generiert. Bitte antworten Sie nicht auf diese E-Mail.</p>
          </div>
        </div>
      </body>
      </html>
    `;

    return { subject, html };
  }

  // Generate admin success email content
  private generateAdminSuccessEmail(certificate: CertificateData, userEmail: string): { subject: string; html: string } {
    const certificateTypeMap: Record<string, string> = {
      'WG/V': 'Wohngebäude Verbrauchsausweis',
      'WG/B': 'Wohngebäude Bedarfsausweis',
      'NWG/V': 'Nichtwohngebäude Verbrauchsausweis'
    };

    const certificateTypeName = certificateTypeMap[certificate.certificate_type] || certificate.certificate_type;
    const buildingAddress = certificate.objektdaten?.strasse && certificate.objektdaten?.hausnummer
      ? `${certificate.objektdaten.strasse} ${certificate.objektdaten.hausnummer}, ${certificate.objektdaten.plz} ${certificate.objektdaten.ort}`
      : 'Nicht verfügbar';

    const subject = `Neue Bestellung - Energieausweis ${certificate.order_number}`;

    const html = `
      <!DOCTYPE html>
      <html>
      <head>
        <meta charset="utf-8">
        <title>Neue Bestellung</title>
        <style>
          body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
          .container { max-width: 700px; margin: 0 auto; padding: 20px; }
          .header { background-color: #1f2937; color: white; padding: 20px; text-align: center; }
          .content { padding: 20px; background-color: #f9f9f9; }
          .details { background-color: white; padding: 15px; margin: 15px 0; border-radius: 5px; }
          .footer { text-align: center; padding: 20px; font-size: 12px; color: #666; }
          table { width: 100%; border-collapse: collapse; margin: 10px 0; }
          th, td { padding: 8px; text-align: left; border-bottom: 1px solid #ddd; }
          th { background-color: #f8f9fa; }
        </style>
      </head>
      <body>
        <div class="container">
          <div class="header">
            <h1>Neue Energieausweis-Bestellung</h1>
          </div>
          <div class="content">
            <p>Eine neue Bestellung ist eingegangen und wurde erfolgreich bezahlt.</p>

            <div class="details">
              <h3>Bestellinformationen:</h3>
              <table>
                <tr><th>Bestellnummer</th><td>${certificate.order_number}</td></tr>
                <tr><th>Zertifikat-ID</th><td>${certificate.id}</td></tr>
                <tr><th>Energieausweis-Typ</th><td>${certificateTypeName}</td></tr>
                <tr><th>Kunde E-Mail</th><td>${userEmail}</td></tr>
                <tr><th>Bestelldatum</th><td>${new Date(certificate.created_at).toLocaleDateString('de-DE', {
                  year: 'numeric',
                  month: 'long',
                  day: 'numeric',
                  hour: '2-digit',
                  minute: '2-digit'
                })}</td></tr>
                <tr><th>Zahlungsstatus</th><td>${certificate.payment_status}</td></tr>
              </table>
            </div>

            <div class="details">
              <h3>Immobilieninformationen:</h3>
              <table>
                <tr><th>Adresse</th><td>${buildingAddress}</td></tr>
                ${certificate.objektdaten?.baujahr ? `<tr><th>Baujahr</th><td>${certificate.objektdaten.baujahr}</td></tr>` : ''}
                ${certificate.objektdaten?.wohnflaeche ? `<tr><th>Wohnfläche</th><td>${certificate.objektdaten.wohnflaeche} m²</td></tr>` : ''}
                ${certificate.objektdaten?.anzahlWohneinheiten ? `<tr><th>Anzahl Wohneinheiten</th><td>${certificate.objektdaten.anzahlWohneinheiten}</td></tr>` : ''}
              </table>
            </div>

            <p><strong>Nächste Schritte:</strong></p>
            <ul>
              <li>Energieausweis erstellen</li>
              <li>Qualitätsprüfung durchführen</li>
              <li>Energieausweis an Kunden senden</li>
            </ul>

            <p>Bestellung im Admin-Dashboard anzeigen: <a href="${Deno.env.get('SITE_URL') || 'https://energieausweis.com'}/admin">Admin-Dashboard</a></p>
          </div>
          <div class="footer">
            <p>Diese E-Mail wurde automatisch generiert.</p>
          </div>
        </div>
      </body>
      </html>
    `;

    return { subject, html };
  }

  // Generate admin failure email content
  private generateAdminFailureEmail(certificate: CertificateData, userEmail: string): { subject: string; html: string } {
    const subject = `Zahlungsfehlschlag - Energieausweis ${certificate.order_number}`;

    const html = `
      <!DOCTYPE html>
      <html>
      <head>
        <meta charset="utf-8">
        <title>Zahlungsfehlschlag</title>
        <style>
          body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
          .container { max-width: 600px; margin: 0 auto; padding: 20px; }
          .header { background-color: #dc2626; color: white; padding: 20px; text-align: center; }
          .content { padding: 20px; background-color: #f9f9f9; }
          .details { background-color: white; padding: 15px; margin: 15px 0; border-radius: 5px; }
          .footer { text-align: center; padding: 20px; font-size: 12px; color: #666; }
          table { width: 100%; border-collapse: collapse; margin: 10px 0; }
          th, td { padding: 8px; text-align: left; border-bottom: 1px solid #ddd; }
          th { background-color: #f8f9fa; }
        </style>
      </head>
      <body>
        <div class="container">
          <div class="header">
            <h1>Zahlungsfehlschlag</h1>
          </div>
          <div class="content">
            <p>Eine Zahlung für eine Energieausweis-Bestellung ist fehlgeschlagen.</p>

            <div class="details">
              <h3>Bestellinformationen:</h3>
              <table>
                <tr><th>Bestellnummer</th><td>${certificate.order_number}</td></tr>
                <tr><th>Zertifikat-ID</th><td>${certificate.id}</td></tr>
                <tr><th>Kunde E-Mail</th><td>${userEmail}</td></tr>
                <tr><th>Bestelldatum</th><td>${new Date(certificate.created_at).toLocaleDateString('de-DE')}</td></tr>
                <tr><th>Zahlungsstatus</th><td>${certificate.payment_status}</td></tr>
              </table>
            </div>

            <p><strong>Erforderliche Maßnahmen:</strong></p>
            <ul>
              <li>Kunde wurde über den Zahlungsfehlschlag informiert</li>
              <li>Kunde kann Zahlung erneut versuchen</li>
              <li>Bei wiederholten Problemen Kunde kontaktieren</li>
            </ul>

            <p>Bestellung im Admin-Dashboard anzeigen: <a href="${Deno.env.get('SITE_URL') || 'https://energieausweis.com'}/admin">Admin-Dashboard</a></p>
          </div>
          <div class="footer">
            <p>Diese E-Mail wurde automatisch generiert.</p>
          </div>
        </div>
      </body>
      </html>
    `;

    return { subject, html };
  }

  // Send email using Resend
  private async sendEmail(to: string, subject: string, html: string): Promise<{ success: boolean; messageId?: string; error?: string }> {
    try {
      const { data, error } = await this.resend.emails.send({
        from: this.fromEmail,
        to: [to],
        subject,
        html,
      });

      if (error) {
        console.error('Resend error:', error);
        return { success: false, error: error.message || 'Unknown Resend error' };
      }

      return { success: true, messageId: data?.id };
    } catch (error) {
      console.error('Email sending error:', error);
      return { success: false, error: error.message || 'Unknown email sending error' };
    }
  }

  // Send customer success notification
  public async sendCustomerSuccessNotification(certificateId: string): Promise<boolean> {
    try {
      const data = await this.getCertificateData(certificateId);
      if (!data) {
        console.error('Could not fetch certificate data for customer success notification');
        return false;
      }

      const { certificate, userEmail } = data;
      const { subject, html } = this.generateCustomerSuccessEmail(certificate, userEmail);

      // Log email attempt
      const logId = await this.logEmail({
        certificate_id: certificateId,
        recipient_email: userEmail,
        email_type: 'customer_success',
        status: 'pending'
      });

      // Send email
      const result = await this.sendEmail(userEmail, subject, html);

      // Update log
      if (logId) {
        await this.updateEmailLog(logId, {
          status: result.success ? 'sent' : 'failed',
          sent_at: result.success ? new Date().toISOString() : undefined,
          error_message: result.error,
          resend_message_id: result.messageId
        });
      }

      if (!result.success) {
        console.error('Failed to send customer success email:', result.error);
        return false;
      }

      console.log(`Customer success email sent to ${userEmail} for certificate ${certificateId}`);
      return true;
    } catch (error) {
      console.error('Error sending customer success notification:', error);
      return false;
    }
  }

  // Send customer failure notification
  public async sendCustomerFailureNotification(certificateId: string): Promise<boolean> {
    try {
      const data = await this.getCertificateData(certificateId);
      if (!data) {
        console.error('Could not fetch certificate data for customer failure notification');
        return false;
      }

      const { certificate, userEmail } = data;
      const { subject, html } = this.generateCustomerFailureEmail(certificate, userEmail);

      // Log email attempt
      const logId = await this.logEmail({
        certificate_id: certificateId,
        recipient_email: userEmail,
        email_type: 'customer_failure',
        status: 'pending'
      });

      // Send email
      const result = await this.sendEmail(userEmail, subject, html);

      // Update log
      if (logId) {
        await this.updateEmailLog(logId, {
          status: result.success ? 'sent' : 'failed',
          sent_at: result.success ? new Date().toISOString() : undefined,
          error_message: result.error,
          resend_message_id: result.messageId
        });
      }

      if (!result.success) {
        console.error('Failed to send customer failure email:', result.error);
        return false;
      }

      console.log(`Customer failure email sent to ${userEmail} for certificate ${certificateId}`);
      return true;
    } catch (error) {
      console.error('Error sending customer failure notification:', error);
      return false;
    }
  }

  // Send admin success notification
  public async sendAdminSuccessNotification(certificateId: string): Promise<boolean> {
    try {
      const data = await this.getCertificateData(certificateId);
      if (!data) {
        console.error('Could not fetch certificate data for admin success notification');
        return false;
      }

      const { certificate, userEmail } = data;
      const { subject, html } = this.generateAdminSuccessEmail(certificate, userEmail);

      // Log email attempt
      const logId = await this.logEmail({
        certificate_id: certificateId,
        recipient_email: this.adminEmail,
        email_type: 'admin_success',
        status: 'pending'
      });

      // Send email
      const result = await this.sendEmail(this.adminEmail, subject, html);

      // Update log
      if (logId) {
        await this.updateEmailLog(logId, {
          status: result.success ? 'sent' : 'failed',
          sent_at: result.success ? new Date().toISOString() : undefined,
          error_message: result.error,
          resend_message_id: result.messageId
        });
      }

      if (!result.success) {
        console.error('Failed to send admin success email:', result.error);
        return false;
      }

      console.log(`Admin success email sent to ${this.adminEmail} for certificate ${certificateId}`);
      return true;
    } catch (error) {
      console.error('Error sending admin success notification:', error);
      return false;
    }
  }

  // Send admin failure notification
  public async sendAdminFailureNotification(certificateId: string): Promise<boolean> {
    try {
      const data = await this.getCertificateData(certificateId);
      if (!data) {
        console.error('Could not fetch certificate data for admin failure notification');
        return false;
      }

      const { certificate, userEmail } = data;
      const { subject, html } = this.generateAdminFailureEmail(certificate, userEmail);

      // Log email attempt
      const logId = await this.logEmail({
        certificate_id: certificateId,
        recipient_email: this.adminEmail,
        email_type: 'admin_failure',
        status: 'pending'
      });

      // Send email
      const result = await this.sendEmail(this.adminEmail, subject, html);

      // Update log
      if (logId) {
        await this.updateEmailLog(logId, {
          status: result.success ? 'sent' : 'failed',
          sent_at: result.success ? new Date().toISOString() : undefined,
          error_message: result.error,
          resend_message_id: result.messageId
        });
      }

      if (!result.success) {
        console.error('Failed to send admin failure email:', result.error);
        return false;
      }

      console.log(`Admin failure email sent to ${this.adminEmail} for certificate ${certificateId}`);
      return true;
    } catch (error) {
      console.error('Error sending admin failure notification:', error);
      return false;
    }
  }

  // Send all notifications for payment success
  public async sendPaymentSuccessNotifications(certificateId: string): Promise<{ customerSent: boolean; adminSent: boolean }> {
    const customerSent = await this.sendCustomerSuccessNotification(certificateId);
    const adminSent = await this.sendAdminSuccessNotification(certificateId);

    return { customerSent, adminSent };
  }

  // Send all notifications for payment failure
  public async sendPaymentFailureNotifications(certificateId: string): Promise<{ customerSent: boolean; adminSent: boolean }> {
    const customerSent = await this.sendCustomerFailureNotification(certificateId);
    const adminSent = await this.sendAdminFailureNotification(certificateId);

    return { customerSent, adminSent };
  }
}